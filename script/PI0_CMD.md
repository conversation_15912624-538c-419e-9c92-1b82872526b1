
# PI0 训练配置

# PI0 微调 - 原始配置
conda activate lerobot && python -m lerobot.scripts.train \
    --policy.path=lerobot/pi0 \
    --dataset.repo_id=stock_cloth_2 \
    --dataset.root=/root/workspace/data/stock_cloth_2 \
    --dataset.episodes="[$(seq -s, 0 999)]" \
    --policy.push_to_hub=false \
    --policy.device=cuda \
    --output_dir=outputs/train/pi0_stock_cloth_effort_chunk000_$(date +%Y%m%d_%H%M%S) \
    --job_name=pi0_stock_cloth_effort_chunk000_training \
    --steps=2000000 \
    --save_freq=5000 \
    --log_freq=200 \
    --wandb.project=stock_cloth_2 \
    --wandb.enable=true \
    --wandb.mode=offline \
    --wandb.disable_artifact=true \
    --batch_size=2 \
    --eval_freq=0 \
    --policy.train_expert_only=true \
    --policy.freeze_vision_encoder=true \
    --policy.train_state_proj=true \
    --dataset.image_transforms.enable=true \
    --dataset.image_transforms.max_num_transforms=2 \
    --dataset.video_backend=pyav


# PI0 微调 - 优化配置（推荐）
conda activate lerobot && python -m lerobot.scripts.train \
    --policy.path=lerobot/pi0 \
    --dataset.repo_id=stock_cloth_2 \
    --dataset.root=/root/workspace/data/stock_cloth_2 \
    --dataset.episodes="[$(seq -s, 0 999)]" \
    --policy.push_to_hub=false \
    --policy.device=cuda \
    --output_dir=outputs/train/pi0_stock_cloth_effort_optimized_$(date +%Y%m%d_%H%M%S) \
    --job_name=pi0_stock_cloth_effort_optimized_training \
    --steps=2000000 \
    --save_freq=5000 \
    --log_freq=200 \
    --wandb.project=lerobot \
    --wandb.enable=true \
    --wandb.mode=offline \
    --wandb.disable_artifact=true \
    --batch_size=8 \
    --eval_freq=0 \
    --policy.train_expert_only=true \
    --policy.freeze_vision_encoder=false \
    --policy.train_state_proj=true \
    --policy.optimizer_lr=5e-5 \
    --policy.scheduler_warmup_steps=2000 \
    --policy.scheduler_decay_steps=50000 \
    --policy.scheduler_decay_lr=5e-6 \
    --dataset.image_transforms.enable=true \
    --dataset.image_transforms.max_num_transforms=3 \
    --dataset.image_transforms.random_order=true \
    --dataset.video_backend=pyav


# PI0 微调 - 全量微调配置（如果优化配置效果仍不好）
conda activate lerobot && python -m lerobot.scripts.train \
    --policy.path=lerobot/pi0 \
    --dataset.repo_id=stock_cloth_2 \
    --dataset.root=/root/workspace/data/stock_cloth_2 \
    --dataset.episodes="[$(seq -s, 0 999)]" \
    --policy.push_to_hub=false \
    --policy.device=cuda \
    --output_dir=outputs/train/pi0_stock_cloth_effort_full_$(date +%Y%m%d_%H%M%S) \
    --job_name=pi0_stock_cloth_effort_full_training \
    --steps=2000000 \
    --save_freq=5000 \
    --log_freq=200 \
    --wandb.project=lerobot \
    --wandb.enable=true \
    --wandb.mode=offline \
    --wandb.disable_artifact=true \
    --batch_size=4 \
    --eval_freq=0 \
    --policy.train_expert_only=false \
    --policy.freeze_vision_encoder=false \
    --policy.train_state_proj=true \
    --policy.optimizer_lr=2e-5 \
    --policy.scheduler_warmup_steps=3000 \
    --policy.scheduler_decay_steps=80000 \
    --policy.scheduler_decay_lr=2e-6 \
    --dataset.image_transforms.enable=true \
    --dataset.image_transforms.max_num_transforms=3 \
    --dataset.image_transforms.random_order=true \
    --dataset.video_backend=pyav


# PI0 resume training - 恢复训练
conda activate lerobot;
python -m lerobot.scripts.train \
    --config_path=outputs/train/pi0_stock_cloth_effort_chunk000_20250720_232952/checkpoints/last/pretrained_model/train_config.json \
    --resume=true \
    --steps=2000000 \
    --save_freq=5000 \
    --wandb.enable=true \
    --wandb.mode=offline \
    --wandb.disable_artifact=true \
    --dataset.image_transforms.enable=true \
    --dataset.image_transforms.max_num_transforms=2 \
    --eval_freq=0 \
    --log_freq=200 



# PI0模型单个episode可视化
python script/pi0eval_episode.py \
    --model_path outputs/train/pi0_stock_cloth_effort_chunk000_20250720_232952/checkpoints/last/pretrained_model \
    --dataset_repo_id stock_cloth_2 \
    --dataset_root /root/workspace/data/stock_cloth_2 \
    --episode_idx 1 \
    --plot_interval 3 \
    --save_debug_images 5 \
    --language_instruction "stock_cloth"